#!/usr/bin/env python3
"""
Training cho Keno Model với 30 kỳ trước để dự đoán kỳ tiếp theo
"""

import tensorflow as tf
import os
import numpy as np
from variable_length_model import connect_db, print_header, print_success, print_info, print_warning
import pandas as pd
from sklearn.model_selection import train_test_split

# Thiết lập GPU
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

class Keno30Model:
    """
    Model sử dụng 30 kỳ trước để dự đoán kỳ tiếp theo

    Logic:
    - Train theo ngày
    - Sử dụng 30 kỳ trước đó để dự đoán kỳ tiếp theo
    - Chỉ đưa ra dự đoán nếu ngày mới có >= 30 kỳ
    """

    def __init__(self, sequence_length=30):
        self.sequence_length = sequence_length  # C<PERSON> định 30 kỳ
        self.model = None
        self.draws_per_day = 119  # 119 kỳ/ng<PERSON><PERSON>

    def create_30_period_features(self, df):
        """Tạo features với 30 kỳ cố định"""
        print_header("TẠO 30-PERIOD FEATURES")
        print_info("🎯 Logic: Dùng 30 kỳ trước đó để dự đoán kỳ tiếp theo")

        dates = df['date'].unique()
        all_sequences = []
        all_targets = []

        valid_dates = 0

        for date in sorted(dates):
            day_data = df[df['date'] == date].copy().reset_index(drop=True)

            if len(day_data) < 31:  # Cần ít nhất 31 kỳ (30 để train + 1 target)
                continue

            valid_dates += 1
            day_sequences = 0

            # Tạo ma trận nhị phân cho ngày này
            numbers_matrix = np.zeros((len(day_data), 80))
            for i, numbers in enumerate(day_data['results']):
                for num in numbers:
                    if 1 <= num <= 80:
                        numbers_matrix[i, num-1] = 1

            # Tạo sequences với 30 kỳ cố định
            for i in range(30, len(numbers_matrix)):  # Bắt đầu từ kỳ 31
                # Lấy 30 kỳ trước đó
                seq = numbers_matrix[i-30:i]  # 30 kỳ trước
                target = numbers_matrix[i]    # Kỳ tiếp theo cần dự đoán

                all_sequences.append(seq)
                all_targets.append(target)
                day_sequences += 1

            if valid_dates <= 3:
                print_info(f"  📅 Ngày {date}: {len(day_data)} kỳ → {day_sequences} sequences")
                print_info(f"      Logic: Kỳ 31 dùng kỳ 1-30, Kỳ 119 dùng kỳ 89-118")
                if len(day_data) >= 119:
                    print_info(f"      ✅ Ngày đầy đủ: {len(day_data)} kỳ")

        print_success(f"✅ Xử lý {valid_dates} ngày")
        print_info(f"📊 Tổng: {len(all_sequences):,} sequences")
        print_info(f"📏 Sequence length cố định: {self.sequence_length} kỳ")

        return np.array(all_sequences), np.array(all_targets)

    def create_model(self):
        """Tạo model với sequence length cố định 30"""
        print_header("TẠO 30-PERIOD MODEL")

        model = tf.keras.Sequential([
            # Input layer - cố định 30 kỳ
            tf.keras.layers.Input(shape=(self.sequence_length, 80)),

            # LSTM layers
            tf.keras.layers.LSTM(64, return_sequences=True, dropout=0.2),
            tf.keras.layers.LSTM(32, dropout=0.2),

            # Dense layers
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(80, activation='sigmoid')
        ])

        # Optimizer
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        print_success("✅ Model 30-period đã được tạo")
        print_info(f"📏 Sequence length: {self.sequence_length}")
        return model

    def train_model(self, X, y, epochs=50, batch_size=32):
        """Train model với 30-period data"""
        print_header("TRAINING 30-PERIOD MODEL")

        # Chia dữ liệu
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        print_info(f"Train: {len(X_train):,}, Test: {len(X_test):,}")

        # Tạo model
        self.model = self.create_model()

        # Callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=5,
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=3,
                min_lr=1e-7,
                verbose=1
            )
        ]

        # Training
        history = self.model.fit(
            X_train, y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_data=(X_test, y_test),
            callbacks=callbacks,
            verbose=1
        )

        print_success("Training hoàn thành")
        return history

    def predict_next_draw(self, last_30_results):
        """
        Dự đoán kỳ tiếp theo từ 30 kỳ trước đó

        Parameters:
        last_30_results: List 30 kỳ gần nhất

        Returns:
        Dự đoán xác suất cho 80 số
        """
        if len(last_30_results) != 30:
            print_warning(f"Cần đúng 30 kỳ, hiện có {len(last_30_results)}")
            return None

        print_info(f"Dự đoán từ 30 kỳ gần nhất")

        # Tạo ma trận nhị phân
        numbers_matrix = np.zeros((30, 80))
        for i, numbers in enumerate(last_30_results):
            for num in numbers:
                if 1 <= num <= 80:
                    numbers_matrix[i, num-1] = 1

        # Dự đoán
        sequence = np.array([numbers_matrix])
        probabilities = self.model.predict(sequence, verbose=0)[0]

        return probabilities

    def predict_missing_numbers(self, last_30_results, num_miss=5):
        """
        Dự đoán số trượt từ 30 kỳ trước đó

        Parameters:
        last_30_results: List 30 kỳ gần nhất
        num_miss: Số lượng số trượt cần dự đoán (mặc định 5)

        Returns:
        List số trượt dự đoán
        """
        probabilities = self.predict_next_draw(last_30_results)

        if probabilities is None:
            return []

        # Tạo danh sách (số, xác suất)
        prob_list = [(i + 1, prob) for i, prob in enumerate(probabilities)]
        prob_list.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

        # Lấy num_miss số có xác suất thấp nhất
        miss_numbers = [num for num, _ in prob_list[:num_miss]]
        miss_numbers.sort()

        return miss_numbers

def setup_gpu():
    """Cấu hình GPU cho Apple Silicon"""
    print_header("CẤU HÌNH APPLE SILICON GPU")

    gpus = tf.config.list_physical_devices('GPU')
    if len(gpus) > 0:
        print_success(f"✅ Phát hiện Apple Silicon GPU: {len(gpus)} device(s)")
        print_info("🍎 MacBook Pro M4 Pro Specs:")
        print_info("   • CPU: 12-Core (8P + 4E)")
        print_info("   • GPU: 16-Core Apple GPU")
        print_info("   • RAM: 24GB Unified Memory")
        print_info("   • Memory Bandwidth: ~273 GB/s")

        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print_success("✅ Đã cấu hình GPU memory growth cho Apple Silicon")
            print_info("🚀 MPS (Metal Performance Shaders) backend activated")
        except Exception as e:
            print_warning(f"Cấu hình GPU: {e}")
    else:
        print_warning("⚠️ Không tìm thấy GPU, sử dụng CPU")

def load_real_data():
    """Tải dữ liệu thực từ database"""
    print_header("TẢI DỮ LIỆU THỰC")

    try:
        conn = connect_db()
        cursor = conn.cursor(dictionary=True)

        # Lấy dữ liệu trước ngày 2025-04-01 để tạo model
        query = """
            SELECT date, time, results
            FROM histories_keno
            WHERE date < '2025-04-01'
            ORDER BY date DESC, time ASC
        """

        print_info("Đang truy vấn database (date < '2025-04-01')...")
        cursor.execute(query)
        rows = cursor.fetchall()

        cursor.close()
        conn.close()

        if len(rows) == 0:
            print_warning("Không có dữ liệu trong database trước ngày 2025-04-01")
            return None

        # Tạo DataFrame
        df = pd.DataFrame(rows)
        df['results'] = df['results'].apply(lambda x: [int(n) for n in x.split(',')])

        print_success(f"✅ Đã tải {len(df):,} records (date < '2025-04-01')")
        print_info(f"📅 Từ {df['date'].min()} đến {df['date'].max()}")
        print_info(f"🗓️ Tổng {df['date'].nunique()} ngày")

        return df

    except Exception as e:
        print_warning(f"Lỗi tải dữ liệu: {e}")
        return None

def analyze_data_quality(df):
    """Phân tích chất lượng dữ liệu cho 30-period model"""
    print_header("PHÂN TÍCH CHẤT LƯỢNG DỮ LIỆU - 30 PERIOD MODEL")

    # Phân tích theo ngày
    daily_counts = df.groupby('date').size()

    print_info(f"📊 THỐNG KÊ THEO NGÀY:")
    print(f"   • Tổng ngày: {len(daily_counts)}")
    print(f"   • Kỳ/ngày - Min: {daily_counts.min()}, Max: {daily_counts.max()}")
    print(f"   • Kỳ/ngày - Trung bình: {daily_counts.mean():.1f}")

    # Ngày có đủ để train 30-period model (cần ít nhất 31 kỳ)
    trainable_days = daily_counts[daily_counts >= 31]
    insufficient_days = daily_counts[daily_counts < 31]

    print_success(f"✅ Ngày có thể train (≥31 kỳ): {len(trainable_days)}")
    if len(insufficient_days) > 0:
        print_warning(f"⚠️ Ngày không đủ (<31 kỳ): {len(insufficient_days)}")

    # Ngày có đủ 119 kỳ
    full_days = daily_counts[daily_counts >= 119]
    print_info(f"🎯 Ngày đầy đủ (≥119 kỳ): {len(full_days)}")

    return {
        'total_days': len(daily_counts),
        'trainable_days': len(trainable_days),
        'full_days': len(full_days),
        'daily_counts': daily_counts
    }

def train_30_period_model():
    """Train model 30-period"""
    print_header("TRAINING 30-PERIOD MODEL")

    # Setup GPU
    setup_gpu()

    # Tải dữ liệu
    df = load_real_data()
    if df is None:
        print_warning("Không thể tải dữ liệu, thoát")
        return None

    # Phân tích dữ liệu
    data_stats = analyze_data_quality(df)

    if data_stats['trainable_days'] < 10:
        print_warning("Không đủ dữ liệu để train (cần ít nhất 10 ngày có ≥31 kỳ)")
        return None

    # Khởi tạo model
    print_info("🚀 Khởi tạo 30-Period Model...")
    model = Keno30Model(sequence_length=30)
    print_info("📏 Sequence length: 30 kỳ cố định")

    # Tạo features
    X, y = model.create_30_period_features(df)

    if len(X) == 0:
        print_warning("Không tạo được features")
        return None

    print_success(f"✅ Sẵn sàng train với {len(X):,} sequences")

    # Training tối ưu cho Apple Silicon M4 Pro
    epochs = 50
    batch_size = 512  # Batch size vừa phải cho 30-period model
    print_info(f"🎯 Bắt đầu training với {epochs} epochs và batch size {batch_size}...")
    print_info("🍎 Tối ưu cho MacBook Pro M4 Pro: 16-Core GPU, 24GB Unified Memory")

    history = model.train_model(X, y, epochs=epochs, batch_size=batch_size)

    if history:
        # Lưu model
        model_path = 'keno_30_period_model.h5'
        model.model.save(model_path)
        print_success(f"✅ Đã lưu model: {model_path}")

        # Lưu thống kê
        import pickle
        stats = {
            'data_stats': data_stats,
            'model_config': {
                'sequence_length': model.sequence_length,
                'draws_per_day': model.draws_per_day,
                'total_sequences': len(X),
                'epochs_trained': epochs,
                'model_type': '30_period'
            }
        }

        with open('model_30_stats.pkl', 'wb') as f:
            pickle.dump(stats, f)

        print_success("✅ Đã lưu thống kê model")

        # Đánh giá chất lượng training
        final_epoch = len(history.history['loss'])
        final_loss = history.history['loss'][-1]
        final_val_loss = history.history['val_loss'][-1]

        print_header("ĐÁNH GIÁ CHẤT LƯỢNG MODEL")
        print_info(f"📊 Dừng ở epoch: {final_epoch}/{epochs}")
        print_info(f"📉 Final loss: {final_loss:.4f}")
        print_info(f"📉 Final val_loss: {final_val_loss:.4f}")

        if final_epoch < 10:
            print_warning("⚠️ Model dừng quá sớm, có thể cần điều chỉnh early stopping")
        elif final_epoch > 25:
            print_success("✅ Model train đủ lâu, chất lượng rất tốt")
        else:
            print_success("✅ Model train hợp lý")

        if final_val_loss > final_loss * 1.2:
            print_warning("⚠️ Có dấu hiệu overfitting")
        else:
            print_success("✅ Không có overfitting")

        return model

    return None

def test_trained_model():
    """Test model đã train"""
    print_header("TEST 30-PERIOD MODEL ĐÃ TRAIN")

    try:
        # Load model
        model = Keno30Model()
        model.model = tf.keras.models.load_model('keno_30_period_model.h5')
        print_success("✅ Đã load model")

        # Test với dữ liệu mẫu
        print_info("🧪 Test với dữ liệu mẫu...")

        # Tạo 30 kỳ test
        test_30_periods = []
        for i in range(30):
            numbers = list(range(1 + i % 12, 81, 3))[:20]
            test_30_periods.append(numbers)

        print_info(f"\n📍 Dự đoán từ 30 kỳ test:")
        predictions = model.predict_missing_numbers(test_30_periods, num_miss=5)

        if predictions:
            print_success(f"   ✅ 5 số trượt dự đoán: {predictions}")

        return True

    except Exception as e:
        print_warning(f"Lỗi test model: {e}")
        return False

def check_day_eligibility(day_data):
    """
    Kiểm tra ngày có đủ điều kiện để dự đoán không

    Parameters:
    day_data: List kết quả các kỳ trong ngày

    Returns:
    bool: True nếu ngày có >= 30 kỳ
    """
    return len(day_data) >= 30

def predict_for_new_day(day_data):
    """
    Dự đoán cho ngày mới (chỉ khi có >= 30 kỳ)

    Parameters:
    day_data: List kết quả các kỳ trong ngày

    Returns:
    dict: Kết quả dự đoán hoặc None nếu không đủ điều kiện
    """
    print_header("DỰ ĐOÁN CHO NGÀY MỚI - 30 PERIOD MODEL")

    if not check_day_eligibility(day_data):
        print_warning(f"❌ Ngày chỉ có {len(day_data)} kỳ, cần ít nhất 30 kỳ để dự đoán")
        return None

    print_success(f"✅ Ngày có {len(day_data)} kỳ, đủ điều kiện dự đoán")

    try:
        # Load model
        model = Keno30Model()
        model.model = tf.keras.models.load_model('keno_30_period_model.h5')

        # Lấy 30 kỳ gần nhất
        last_30 = day_data[-30:]

        # Dự đoán
        predictions = model.predict_missing_numbers(last_30, num_miss=5)

        result = {
            'eligible': True,
            'total_periods': len(day_data),
            'used_periods': 30,
            'predictions': predictions,
            'model_type': '30_period'
        }

        print_success(f"🎯 Dự đoán 5 số trượt: {predictions}")

        return result

    except Exception as e:
        print_warning(f"Lỗi dự đoán: {e}")
        return None

def main():
    """Main function"""
    print_header("KENO 30-PERIOD MODEL TRAINING")
    print_info("Logic: Dùng 30 kỳ trước đó để dự đoán kỳ tiếp theo")
    print_info("Điều kiện: Chỉ dự đoán nếu ngày mới có >= 30 kỳ")
    print_info("Dữ liệu: Chỉ sử dụng date < '2025-04-01' để tạo model")
    print()

    try:
        # Training
        model = train_30_period_model()

        if model:
            print_header("TRAINING THÀNH CÔNG")

            # Test model
            test_success = test_trained_model()

            if test_success:
                print_header("HOÀN THÀNH")
                print_success("✅ Model 30-period đã sẵn sàng sử dụng!")
                print_info("📁 Files được tạo:")
                print("   • keno_30_period_model.h5 - Model đã train")
                print("   • model_30_stats.pkl - Thống kê training")
                print()
                print_info("🚀 Đặc điểm model:")
                print("   • Sử dụng 30 kỳ trước để dự đoán kỳ tiếp theo")
                print("   • Chỉ dự đoán nếu ngày có >= 30 kỳ")
                print("   • Dự đoán 5 số trượt (thay vì 6)")
                print()
                print_info("💡 Sử dụng:")
                print("   from train_keno_30_model import predict_for_new_day")
                print("   result = predict_for_new_day(day_data)")
            else:
                print_warning("⚠️ Model train thành công nhưng test có vấn đề")
        else:
            print_warning("❌ Training không thành công")

    except Exception as e:
        print_warning(f"Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
